using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;

public class Score : MonoBehaviour
{
    public static int score = 0;
    [SerializeField]
    private TMP_Text scoreText;

    private static int lastDisplayedScore = -1;

    // Start is called before the first frame update
    private void Start()
    {
        score = 0;
        lastDisplayedScore = -1;
        UpdateScoreText();
    }

    private void Update()
    {
        // Only update text when score actually changes
        if (score != lastDisplayedScore)
        {
            UpdateScoreText();
        }
    }

    private void UpdateScoreText()
    {
        if (scoreText == null) return;

        scoreText.text = score.ToString();
        lastDisplayedScore = score;
    }

    // Public method to add score and trigger update
    public static void AddScore(int points)
    {
        score += points;
    }
}
