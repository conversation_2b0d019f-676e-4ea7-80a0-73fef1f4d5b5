<dependencies>
  <packages>
    <package>androidx.lifecycle:lifecycle-common-java8:2.4.1</package>
    <package>androidx.lifecycle:lifecycle-process:2.4.1</package>
    <package>com.google.android.gms:play-services-ads:21.3.0</package>
    <package>com.google.android.gms:play-services-base:18.1.0</package>
    <package>com.google.android.gms:play-services-base:18.6.0</package>
    <package>com.google.android.ump:user-messaging-platform:2.0.0</package>
    <package>com.google.firebase:firebase-analytics:21.2.0</package>
    <package>com.google.firebase:firebase-analytics:22.4.0</package>
    <package>com.google.firebase:firebase-app-unity:12.9.0</package>
    <package>com.google.firebase:firebase-auth:23.2.0</package>
    <package>com.google.firebase:firebase-auth-unity:12.9.0</package>
    <package>com.google.firebase:firebase-common:21.0.0</package>
    <package>com.google.firebase:firebase-crashlytics-ndk:18.3.2</package>
    <package>com.google.firebase:firebase-crashlytics-unity:10.3.0</package>
    <package>com.google.firebase:firebase-database:20.1.0</package>
    <package>com.google.firebase:firebase-database-unity:10.3.0</package>
    <package>com.google.firebase:firebase-firestore:24.4.1</package>
    <package>com.google.firebase:firebase-firestore-unity:10.3.0</package>
    <package>com.google.firebase:firebase-storage:20.1.0</package>
    <package>com.google.firebase:firebase-storage-unity:10.3.0</package>
    <package>com.unity3d.mediation:admob-adapter:[1.0,2.0[</package>
    <package>com.unity3d.mediation:mediation-sdk:[1.0,2.0[</package>
    <package>com.unity3d.mediation:unityads-adapter:[1.0,2.0[</package>
  </packages>
  <files />
  <settings>
    <setting name="androidAbis" value="arm64-v8a,armeabi-v7a" />
    <setting name="bundleId" value="com.Gamestorme.Skybound" />
    <setting name="explodeAars" value="True" />
    <setting name="gradleBuildEnabled" value="True" />
    <setting name="gradlePropertiesTemplateEnabled" value="True" />
    <setting name="gradleTemplateEnabled" value="True" />
    <setting name="installAndroidPackages" value="True" />
    <setting name="localMavenRepoDir" value="Assets/GeneratedLocalRepo" />
    <setting name="packageDir" value="Assets/Plugins/Android" />
    <setting name="patchAndroidManifest" value="True" />
    <setting name="patchMainTemplateGradle" value="True" />
    <setting name="projectExportEnabled" value="False" />
    <setting name="useFullCustomMavenRepoPathWhenExport" value="True" />
    <setting name="useFullCustomMavenRepoPathWhenNotExport" value="False" />
    <setting name="useJetifier" value="True" />
  </settings>
</dependencies>