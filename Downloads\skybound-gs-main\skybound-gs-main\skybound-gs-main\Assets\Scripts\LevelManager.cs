using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class LevelManager : MonoBehaviour
{
    public GameObject LoadingScreen;
    public Slider LoadingBar;
    public float speed;
    public int nextScene;

    public void LoadScene(int sceneId)
    {
        nextScene = sceneId;
        StartCoroutine(LoadSceneAsync());
    }

    IEnumerator LoadSceneAsync()
    {
        AsyncOperation operation = SceneManager.LoadSceneAsync(nextScene);
        operation.allowSceneActivation = false;

        LoadingScreen.SetActive(true);

        while (!operation.isDone)
        {
            // Update progress bar (0.0 to 0.9 is loading, 0.9 to 1.0 is activation)
            float progress = Mathf.Clamp01(operation.progress / 0.9f);
            LoadingBar.value = progress;

            // When loading reaches 90%, allow scene activation
            if (operation.progress >= 0.9f)
            {
                // Optional: Add a small delay for visual feedback
                yield return new WaitForSeconds(0.5f);
                operation.allowSceneActivation = true;
            }

            yield return null;
        }
    }
}
