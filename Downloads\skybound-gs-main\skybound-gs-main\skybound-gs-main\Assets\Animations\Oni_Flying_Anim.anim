%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Oni_Flying_Anim
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves:
  - curve:
    - time: 0
      value: {fileID: 21300000, guid: 0ee4e75abb088dd44b71a5e0bb699322, type: 3}
    - time: 0.13333334
      value: {fileID: 21300000, guid: cb30ac1876791b54ab34f79c20f9bce1, type: 3}
    - time: 0.26666668
      value: {fileID: 21300000, guid: d9c336148e3ae2a43bd98334a96046a3, type: 3}
    - time: 0.4
      value: {fileID: 21300000, guid: 50b86f83b24ac4045888d76a58e41f68, type: 3}
    - time: 0.53333336
      value: {fileID: 21300000, guid: 2342ade444bb2154ab8f180425591d13, type: 3}
    attribute: m_Sprite
    path: 
    classID: 212
    script: {fileID: 0}
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 0
      script: {fileID: 0}
      typeID: 212
      customType: 23
      isPPtrCurve: 1
    pptrCurveMapping:
    - {fileID: 21300000, guid: 0ee4e75abb088dd44b71a5e0bb699322, type: 3}
    - {fileID: 21300000, guid: cb30ac1876791b54ab34f79c20f9bce1, type: 3}
    - {fileID: 21300000, guid: d9c336148e3ae2a43bd98334a96046a3, type: 3}
    - {fileID: 21300000, guid: 50b86f83b24ac4045888d76a58e41f68, type: 3}
    - {fileID: 21300000, guid: 2342ade444bb2154ab8f180425591d13, type: 3}
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 0.55
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves: []
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
