using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;

public class MenuButtons : MonoBehaviour
{
    [SerializeField]
    GameObject pickerWheel;
    [SerializeField]
    GameObject objecttoHide;
    [SerializeField]
    GameObject[] Popup;
    [SerializeField]
    GameObject Image;
    [SerializeField]
    GameObject ClaimButton;
    [SerializeField]
    GameObject SpinButton;
    public void SpinnerWheelButton()
    {
        pickerWheel.SetActive(true);
        Image.SetActive(false);
        Popup[0].SetActive(false);
        Popup[1].SetActive(false);
        objecttoHide.SetActive(false);
        SpinButton.SetActive(true);
        ClaimButton.SetActive(false);
    }
    // Start is called before the first frame update
    public void Endeless()
    {
        //Will Load skin selection Menu
        FastLoadingManager.LoadScene(3);
    }

    // Update is called once per frame
    public void Ranking()
    {
        //Will <PERSON> ranking leaderboard
        FastLoadingManager.LoadScene(7);
    }
        public void Back2()
    {
        //Will Return Player to Menu
        FastLoadingManager.LoadScene(6);
    }
            public void Back3()
    {
        //Will Return Player to Menu
        FastLoadingManager.LoadScene(2);
    }
    public void Shop()
    {
        //Will load Shop Menu
        FastLoadingManager.LoadScene(9);
    }
    public void DragonLair()
    {
        //Will load inventory
        FastLoadingManager.LoadScene(6);
    }
    public void Back()
    {
        //Will Return Player to Menu
        FastLoadingManager.LoadScene(0);
    }
    public void Rewards()
    {
        //Will Return Player to Tittle Screen
        FastLoadingManager.LoadScene(8);
    }
}
