-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.ref.dll"
-define:UNITY_2022_1_20
-define:UNITY_2022_1
-define:UNITY_2022
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:USE_SEARCH_ENGINE_API
-define:USE_SEARCH_TABLE
-define:USE_SEARCH_MODULE
-define:USE_PROPERTY_DATABASE
-define:USE_QUERY_BUILDER
-define:USE_SEARCH_EXTENSION_API
-define:SCENE_TEMPLATE_MODULE
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_UNET
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_COLLAB
-define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_UNET
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:PLATFORM_STANDALONE
-define:TEXTCORE_1_0_OR_NEWER
-define:PLATFORM_STANDALONE_WIN
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:ENABLE_WEBSOCKET_HOST
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TIMELINE_FRAMEACCURATE
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/ExternalDependencyManager/Editor/1.2.186/Google.IOSResolver.dll"
-r:"Assets/ExternalDependencyManager/Editor/1.2.186/Google.JarResolver.dll"
-r:"Assets/ExternalDependencyManager/Editor/1.2.186/Google.PackageManagerResolver.dll"
-r:"Assets/ExternalDependencyManager/Editor/1.2.186/Google.VersionHandlerImpl.dll"
-r:"Assets/ExternalDependencyManager/Editor/Google.VersionHandler.dll"
-r:"Assets/Firebase/Editor/Firebase.Crashlytics.Editor.dll"
-r:"Assets/Firebase/Editor/Firebase.Editor.dll"
-r:"Assets/Firebase/Editor/Firebase.Messaging.Editor.dll"
-r:"Assets/Firebase/Plugins/Firebase.Analytics.dll"
-r:"Assets/Firebase/Plugins/Firebase.App.dll"
-r:"Assets/Firebase/Plugins/Firebase.Auth.dll"
-r:"Assets/Firebase/Plugins/Firebase.Crashlytics.dll"
-r:"Assets/Firebase/Plugins/Firebase.Database.dll"
-r:"Assets/Firebase/Plugins/Firebase.Firestore.dll"
-r:"Assets/Firebase/Plugins/Firebase.Installations.dll"
-r:"Assets/Firebase/Plugins/Firebase.Messaging.dll"
-r:"Assets/Firebase/Plugins/Firebase.Platform.dll"
-r:"Assets/Firebase/Plugins/Firebase.Storage.dll"
-r:"Assets/Firebase/Plugins/Firebase.TaskExtension.dll"
-r:"Assets/Firebase/Plugins/Google.MiniJson.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Common.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Core.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Ump.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Ump.Unity.dll"
-r:"Assets/GoogleMobileAds/GoogleMobileAds.Unity.dll"
-r:"Assets/Parse/Plugins/dotNet45/Unity.Compat.dll"
-r:"Assets/Parse/Plugins/dotNet45/Unity.Tasks.dll"
-r:"Assets/Plugins/Demigiant/DOTween/DOTween.dll"
-r:"Assets/Plugins/Demigiant/DOTween/Editor/DOTweenEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEditor.PackageManagerUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIServiceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsNativeModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"C:/Program Files/Unity/Hub/Editor/2022.1.20f1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"C:/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/skybound-gs-main/Library/PackageCache/com.unity.collab-proxy@1.17.7/Lib/Editor/PlasticSCM/log4netPlastic.dll"
-r:"C:/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/skybound-gs-main/Library/PackageCache/com.unity.collab-proxy@1.17.7/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll"
-r:"C:/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/skybound-gs-main/Library/PackageCache/com.unity.collab-proxy@1.17.7/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll"
-r:"C:/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/skybound-gs-main/Library/PackageCache/com.unity.collab-proxy@1.17.7/Lib/Editor/PlasticSCM/unityplastic.dll"
-r:"C:/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/skybound-gs-main/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll"
-r:"C:/Users/<USER>/Downloads/skybound-gs-main/skybound-gs-main/skybound-gs-main/Library/PackageCache/com.unity.nuget.newtonsoft-json@3.0.2/Runtime/Newtonsoft.Json.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Actions/ActionContext.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Actions/ActionManager.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Actions/ClipAction.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Actions/ClipsActions.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Actions/IAction.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Actions/IMenuChecked.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Actions/IMenuName.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Actions/Invoker.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Actions/MarkerAction.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Actions/MarkerActions.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Actions/Menus/MenuItemActionBase.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Actions/Menus/TimelineContextMenu.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Actions/TimelineAction.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Actions/TimelineActions.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Actions/TrackAction.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Actions/TrackActions.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Activation/ActivationTrackEditor.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Activation/ActivationTrackInspector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Analytics/TimelineAnalytics.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Animation/AnimationClipActions.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Animation/AnimationClipCurveCache.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Animation/AnimationClipExtensions.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Animation/AnimationOffsetMenu.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Animation/AnimationPlayableAssetEditor.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Animation/AnimationTrackActions.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Animation/BindingSelector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Animation/BindingTreeViewDataSource.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Animation/BindingTreeViewDataSourceGUI.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Animation/ClipCurveEditor.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Animation/CurveDataSource.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Animation/CurvesProxy.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Animation/CurveTreeViewNode.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Animation/TimelineAnimationUtilities.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Attributes/ActiveInModeAttribute.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Attributes/MenuEntryAttribute.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Attributes/ShortcutAttribute.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Attributes/TimelineShortcutAttribute.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Audio/AudioClipPropertiesDrawer.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Audio/AudioPlayableAssetEditor.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Audio/AudioPlayableAssetInspector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Audio/AudioTrackInspector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/ControlTrack/ControlPlayableAssetEditor.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/CurveEditUtility.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/CustomEditors/ClipEditor.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/CustomEditors/CustomTimelineEditorCache.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/CustomEditors/MarkerEditor.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/CustomEditors/MarkerTrackEditor.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/CustomEditors/TrackEditor.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/DirectorNamedColor.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/DirectorStyles.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Extensions/AnimatedParameterExtensions.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Extensions/AnimationTrackExtensions.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Extensions/TrackExtensions.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/AnimationPlayableAssetInspector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/AnimationTrackInspector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/BasicAssetInspector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/BuiltInCurvePresets.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/ClipInspector/ClipInspector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/ClipInspector/ClipInspectorCurveEditor.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/ClipInspector/ClipInspectorSelectionInfo.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/CurvesOwner/CurvesOwnerInspectorHelper.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/CurvesOwner/ICurvesOwnerInspectorWrapper.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/DirectorNamedColorInspector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/EditorClip.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/EditorClipFactory.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/FrameRateDrawer.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/GroupTrackInspector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/IInspectorChangeHandler.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/MarkerInspector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/TimeFieldDrawer.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/TimelineAssetInspector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/TimelineInspectorUtility.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/TimelinePreferences.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/TimelineProjectSettings.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/inspectors/TrackAssetInspector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Items/ClipItem.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Items/ItemsGroup.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Items/ItemsPerTrack.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Items/ItemsUtils.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Items/ITimelineItem.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Items/MarkerItem.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Localization/Localization.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/AddDelete/AddDeleteItemModeMix.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/AddDelete/AddDeleteItemModeReplace.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/AddDelete/AddDeleteItemModeRipple.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/AddDelete/IAddDeleteItemMode.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Cursors/TimelineCursors.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/EditMode.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/EditModeInputHandler.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/HeaderSplitterManipulator.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Move/IMoveItemMode.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Move/MoveItemHandler.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Move/MoveItemModeMix.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Move/MoveItemModeReplace.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Move/MoveItemModeRipple.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Move/MovingItems.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Sequence/EaseClip.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Sequence/Jog.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Sequence/MarkerHeaderTrackManipulator.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Sequence/RectangleSelect.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Sequence/RectangleTool.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Sequence/RectangleZoom.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Sequence/SelectAndMoveItem.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Sequence/TrackZoom.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Sequence/TrimClip.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/TimeAreaAutoPanner.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/TimeIndicator.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/TimelineClipGroup.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Trim/ITrimItemMode.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Trim/TrimItemModeMix.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Trim/TrimItemModeReplace.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Trim/TrimItemModeRipple.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Utils/EditModeGUIUtils.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Utils/EditModeMixUtils.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Utils/EditModeReplaceUtils.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Utils/EditModeRippleUtils.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Utils/EditModeUtils.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Utils/ManipulatorsUtils.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Manipulators/Utils/PlacementValidity.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/MenuPriority.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Playables/ControlPlayableInspector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Properties/AssemblyInfo.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Recording/AnimationTrackRecorder.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Recording/TimelineRecording.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Recording/TimelineRecordingContextualResponder.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Recording/TimelineRecording_Monobehaviour.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Recording/TimelineRecording_PlayableAsset.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Recording/TrackAssetRecordingExtensions.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Shortcuts.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Signals/SignalAssetInspector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Signals/SignalEmitterEditor.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Signals/SignalEmitterInspector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Signals/SignalEventDrawer.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Signals/SignalManager.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Signals/SignalReceiverHeader.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Signals/SignalReceiverInspector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Signals/SignalUtility.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Signals/Styles.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Signals/TreeView/SignalListFactory.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Signals/TreeView/SignalReceiverItem.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Signals/TreeView/SignalReceiverTreeView.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/State/ISequenceState.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/State/PlayRange.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/State/SequenceHierarchy.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/State/SequencePath.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/State/SequenceState.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/State/WindowState.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/TimelineEditor.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/TimelineHelpers.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/TimelineSelection.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/TimelineUtility.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Tooltip.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Trackhead.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/AnimationTrackKeyDataSource.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/Control.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/Drawers/AnimationTrackDrawer.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/Drawers/ClipDrawer.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/Drawers/InfiniteTrackDrawer.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/Drawers/Layers/ClipsLayer.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/Drawers/Layers/ItemsLayer.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/Drawers/Layers/MarkersLayer.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/Drawers/TrackDrawer.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/Drawers/TrackItemsDrawer.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/IPropertyKeyDataSource.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/IRowGUI.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/ItemGui/ISelectable.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/ItemGui/TimelineClipGUI.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/ItemGui/TimelineItemGUI.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/ItemGui/TimelineMarkerClusterGUI.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/ItemGui/TimelineMarkerGUI.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/ManipulationsClips.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/ManipulationsTimeline.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/ManipulationsTracks.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/Manipulator.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/PickerUtils.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/Snapping/IAttractable.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/Snapping/ISnappable.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/Snapping/SnapEngine.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/TimelineClipHandle.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/TimelineClipUnion.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/TimelineDataSource.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/TimelineDragging.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/TimelineTreeView.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/TimelineTreeViewGUI.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/TrackGui/InlineCurveEditor.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/TrackGui/TimelineGroupGUI.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/TrackGui/TimelineTrackBaseGUI.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/TrackGui/TimelineTrackErrorGUI.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/TrackGui/TimelineTrackGUI.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/TrackGui/TrackResizeHandle.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/treeview/TrackPropertyCurvesDataSource.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Undo/ApplyDefaultUndoAttribute.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Undo/UndoExtensions.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Undo/UndoScope.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/UnityEditorInternals.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/AnimatedParameterCache.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/AnimatedParameterUtility.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/AnimatedPropertyUtility.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/BindingUtility.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/BreadcrumbDrawer.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/Clipboard.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/ClipModifier.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/ControlPlayableUtility.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/CustomTrackDrawerAttribute.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/DisplayNameHelper.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/FileUtility.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/FrameRateDisplayUtility.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/Graphics.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/KeyTraverser.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/MarkerModifier.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/ObjectExtension.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/ObjectReferenceField.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/PreviewedBindings.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/PropertyCollector.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/Range.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/Scopes/GUIColorOverride.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/Scopes/GUIGroupScope.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/Scopes/GUIMixedValueScope.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/Scopes/GUIViewportScope.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/Scopes/HorizontalScope.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/Scopes/IndentLevelScope.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/Scopes/LabelWidthScope.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/Scopes/PropertyScope.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/SequenceSelectorNameFormater.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/SpacePartitioner.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/StyleManager.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/StyleNormalColorOverride.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/TimeFormat.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/TimelineKeyboardNavigation.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/TimeReferenceUtility.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/TrackModifier.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/TrackResourceCache.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Utilities/TypeUtility.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/Modes/TimelineActiveMode.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/Modes/TimelineAssetEditionMode.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/Modes/TimelineDisabledMode.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/Modes/TimelineInactiveMode.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/Modes/TimelineMode.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/Modes/TimelineReadOnlyMode.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/Modes/TimeReferenceMode.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/OverlayDrawer.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/PlaybackScroller.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/SequenceContext.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineEditorWindow.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineMarkerHeaderGUI.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineNavigator.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelinePlaybackControls.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindowAnalytics.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindowTimeControl.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_ActiveTimeline.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_Breadcrumbs.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_Duration.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_EditorCallbacks.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_Gui.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_HeaderGui.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_Manipulators.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_Navigator.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_PlayableLookup.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_PlaybackControls.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_PlayRange.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_PreviewPlayMode.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_Selection.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_StateChange.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_TimeArea.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_TimeCursor.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/TimelineWindow_TrackGui.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/ViewModel/ScriptableObjectViewPrefs.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/ViewModel/TimelineAssetViewModel.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/ViewModel/TimelineAssetViewModel_versions.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/ViewModel/TimelineWindowViewPrefs.cs"
"Library/PackageCache/com.unity.timeline@1.7.5/Editor/Window/WindowConstants.cs"
-langversion:9.0

/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319

/nowarn:0169
/nowarn:0649
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US

-warn:0