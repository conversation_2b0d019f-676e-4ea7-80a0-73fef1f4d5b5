﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RingGenerator : MonoBehaviour
{
    [Header("Ring Generation")]
    public float maxTime = 1;
    public GameObject ring;
    public float height;

    [Header("Object Pooling")]
    public int poolSize = 10;

    private float timer = 0;
    private Queue<GameObject> ringPool = new Queue<GameObject>();
    private List<GameObject> activeRings = new List<GameObject>();

    // Start is called before the first frame update
    void Start()
    {
        InitializePool();
        SpawnRing();
    }

    void InitializePool()
    {
        // Pre-instantiate rings for object pooling
        for (int i = 0; i < poolSize; i++)
        {
            GameObject pooledRing = Instantiate(ring);
            pooledRing.SetActive(false);
            ringPool.Enqueue(pooledRing);
        }
    }

    // Update is called once per frame
    void Update()
    {
        if (timer > maxTime)
        {
            SpawnRing();
            timer = 0;
        }

        timer += Time.deltaTime;

        // Clean up rings that are too far away
        CleanupDistantRings();
    }

    void SpawnRing()
    {
        GameObject newRing = GetPooledRing();
        if (newRing != null)
        {
            newRing.transform.position = transform.position + new Vector3(0, Random.Range(-height, height), 0);
            newRing.SetActive(true);
            activeRings.Add(newRing);
        }
    }

    GameObject GetPooledRing()
    {
        if (ringPool.Count > 0)
        {
            return ringPool.Dequeue();
        }

        // If pool is empty, create a new ring
        return Instantiate(ring);
    }

    void CleanupDistantRings()
    {
        for (int i = activeRings.Count - 1; i >= 0; i--)
        {
            if (activeRings[i] != null && activeRings[i].transform.position.x < transform.position.x - 15f)
            {
                ReturnToPool(activeRings[i]);
                activeRings.RemoveAt(i);
            }
        }
    }

    void ReturnToPool(GameObject ringToReturn)
    {
        ringToReturn.SetActive(false);
        ringPool.Enqueue(ringToReturn);
    }
}
