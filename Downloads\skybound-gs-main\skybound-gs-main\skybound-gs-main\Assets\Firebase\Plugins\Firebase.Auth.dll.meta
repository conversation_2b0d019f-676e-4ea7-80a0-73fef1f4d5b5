fileFormatVersion: 2
guid: 275bd6b96a28470986154b9a995e191c
labels:
- gvh
- gvh_version-12.9.0
- gvhp_exportpath-Firebase/Plugins/Firebase.Auth.dll
timeCreated: 1480838400
PluginImporter:
  serializedVersion: 1
  iconMap: {}
  executionOrder: {}
  isPreloaded: 0
  platformData:
    Android:
      enabled: 1
      settings:
        CPU: AnyCPU
    Any:
      enabled: 0
      settings: {}
    Editor:
      enabled: 1
      settings:
        CPU: AnyCPU
        DefaultValueInitialized: true
        OS: AnyOS
    Linux:
      enabled: 1
      settings:
        CPU: x86
    Linux64:
      enabled: 1
      settings:
        CPU: x86_64
    LinuxUniversal:
      enabled: 1
      settings:
        CPU: AnyCPU
    OSXIntel:
      enabled: 1
      settings:
        CPU: x86
    OSXIntel64:
      enabled: 1
      settings:
        CPU: x86_64
    OSXUniversal:
      enabled: 1
      settings:
        CPU: AnyCPU
    Web:
      enabled: 0
      settings: {}
    WebStreamed:
      enabled: 0
      settings: {}
    Win:
      enabled: 1
      settings:
        CPU: x86
    Win64:
      enabled: 1
      settings:
        CPU: x86_64
    WindowsStoreApps:
      enabled: 0
      settings:
        CPU: AnyCPU
    iOS:
      enabled: 0
      settings:
        CompileFlags:
        FrameworkDependencies:
    tvOS:
      enabled: 0
      settings:
        CompileFlags:
        FrameworkDependencies:
  userData:
  assetBundleName:
  assetBundleVariant:
