fileFormatVersion: 2
guid: 7fe078a56dbf4eb48ee6a1c1eca19ebd
labels:
- gvh
- gvh_version-6.13.0
- gvhp_exportpath-Firebase/Plugins/x86_64/FirebaseCppApp-6_13_0.so
- gvhp_exportpath-Firebase\Plugins\x86_64\FirebaseCppApp-6_13_0.so
PluginImporter:
  externalObjects: {}
  serializedVersion: 2
  iconMap: {}
  executionOrder: {}
  defineConstraints: []
  isPreloaded: 0
  isOverridable: 0
  isExplicitlyReferenced: 0
  validateReferences: 1
  platformData:
  - first:
      : Linux
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      : LinuxUniversal
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      : OSXIntel
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      : OSXIntel64
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      : Web
    second:
      enabled: 0
      settings: {}
  - first:
      : WebStreamed
    second:
      enabled: 0
      settings: {}
  - first:
      Android: Android
    second:
      enabled: 0
      settings:
        CPU: AnyCPU
  - first:
      Any: 
    second:
      enabled: 0
      settings: {}
  - first:
      Editor: Editor
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
        DefaultValueInitialized: true
        OS: AnyOS
  - first:
      Standalone: Linux64
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      Standalone: OSXUniversal
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      Standalone: Win
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      Standalone: Win64
    second:
      enabled: 0
      settings:
        CPU: None
  - first:
      Windows Store Apps: WindowsStoreApps
    second:
      enabled: 0
      settings:
        CPU: AnyCPU
  - first:
      iPhone: iOS
    second:
      enabled: 0
      settings:
        CompileFlags: 
        FrameworkDependencies: 
  userData: 
  assetBundleName: 
  assetBundleVariant: 
