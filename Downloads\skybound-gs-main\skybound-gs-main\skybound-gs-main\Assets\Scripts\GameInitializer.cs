using System.Collections;
using UnityEngine;
using Firebase;
using Firebase.Auth;
using UnityEngine.SceneManagement;
using TMPro;

public class GameInitializer : MonoBehaviour
{
    [Header("Initialization UI")]
    public GameObject loadingPanel;
    public TMP_Text statusText;
    public UnityEngine.UI.Slider progressBar;

    [Header("Scene Management")]
    public int mainMenuScene = 2;
    public int loginScene = 1;

    private bool firebaseInitialized = false;
    private bool adsInitialized = false;
    private bool performanceOptimized = false;

    private void Start()
    {
        StartCoroutine(InitializeGame());
    }

    private IEnumerator InitializeGame()
    {
        if (loadingPanel != null)
            loadingPanel.SetActive(true);

        UpdateStatus("Initializing game...", 0f);

        // Step 1: Performance Optimization
        yield return StartCoroutine(InitializePerformance());
        UpdateStatus("Performance optimized", 0.2f);

        // Step 2: Firebase Initialization
        yield return StartCoroutine(InitializeFirebase());
        UpdateStatus("Firebase initialized", 0.5f);

        // Step 3: Ads Initialization
        yield return StartCoroutine(InitializeAds());
        UpdateStatus("Ads initialized", 0.7f);

        // Step 4: Load User Data
        yield return StartCoroutine(LoadUserData());
        UpdateStatus("User data loaded", 0.9f);

        // Step 5: Complete Initialization
        UpdateStatus("Initialization complete!", 1.0f);
        yield return new WaitForSeconds(0.5f);

        // Navigate to appropriate scene
        NavigateToNextScene();
    }

    private IEnumerator InitializePerformance()
    {
        try
        {
            // Apply performance settings
            Application.targetFrameRate = 60;
            QualitySettings.vSyncCount = 0;

            if (Application.isMobilePlatform)
            {
                QualitySettings.SetQualityLevel(2, true); // Medium quality for mobile
                Physics2D.simulationMode = SimulationMode2D.FixedUpdate;
                Time.fixedDeltaTime = 1f / 50f;
            }

            performanceOptimized = true;
            yield return new WaitForEndOfFrame();
        }
        catch (System.Exception e)
        {
            Debug.LogError("Performance optimization failed: " + e.Message);
        }
    }

    private IEnumerator InitializeFirebase()
    {
        bool initializationComplete = false;

        FirebaseApp.CheckAndFixDependenciesAsync().ContinueWith(task =>
        {
            var dependencyStatus = task.Result;
            if (dependencyStatus == DependencyStatus.Available)
            {
                try
                {
                    var auth = FirebaseAuth.DefaultInstance;
                    auth.StateChanged += OnAuthStateChanged;
                    firebaseInitialized = true;
                    Debug.Log("Firebase initialized successfully");
                }
                catch (System.Exception e)
                {
                    Debug.LogError("Firebase Auth initialization failed: " + e.Message);
                    firebaseInitialized = false;
                }
            }
            else
            {
                Debug.LogError("Could not resolve all Firebase dependencies: " + dependencyStatus);
                firebaseInitialized = false;
            }
            initializationComplete = true;
        }, System.Threading.Tasks.TaskScheduler.FromCurrentSynchronizationContext());

        // Wait for Firebase initialization to complete
        float timeout = 15f; // Increased timeout for better reliability
        float timer = 0f;

        while (!initializationComplete && timer < timeout)
        {
            timer += Time.deltaTime;
            yield return null;
        }

        if (!initializationComplete)
        {
            Debug.LogWarning("Firebase initialization timed out");
            firebaseInitialized = false; // Ensure we don't proceed with uninitialized Firebase
        }
    }

    private void OnAuthStateChanged(object sender, System.EventArgs eventArgs)
    {
        FirebaseAuth auth = FirebaseAuth.DefaultInstance;
        if (auth.CurrentUser != null)
        {
            Debug.Log("User is signed in: " + auth.CurrentUser.DisplayName);
        }
        else
        {
            Debug.Log("User is not signed in");
        }
    }

    private IEnumerator InitializeAds()
    {
        try
        {
            // Initialize ads if AdsInitializer exists
            AdsInitializer adsInit = FindObjectOfType<AdsInitializer>();
            if (adsInit != null)
            {
                adsInit.InitializeAds();
                adsInitialized = true;
            }
            else
            {
                Debug.LogWarning("AdsInitializer not found");
                adsInitialized = true; // Continue without ads
            }

            yield return new WaitForSeconds(1f); // Give ads time to initialize
        }
        catch (System.Exception e)
        {
            Debug.LogError("Ads initialization failed: " + e.Message);
            adsInitialized = true; // Continue without ads
        }
    }

    private IEnumerator LoadUserData()
    {
        try
        {
            // Load saved preferences
            float musicVolume = PlayerPrefs.GetFloat("MUSIC_VOL", 1f);
            int selectedCharacter = PlayerPrefs.GetInt("selectedCharacter", 0);
            int selectedMap = PlayerPrefs.GetInt("selectedMap", 0);

            // Apply audio settings
            AudioListener.volume = musicVolume;

            Debug.Log($"User data loaded - Character: {selectedCharacter}, Map: {selectedMap}");

            yield return new WaitForEndOfFrame();
        }
        catch (System.Exception e)
        {
            Debug.LogError("Failed to load user data: " + e.Message);
        }
    }

    private void NavigateToNextScene()
    {
        if (loadingPanel != null)
            loadingPanel.SetActive(false);

        // Check if user is logged in
        if (firebaseInitialized && FirebaseAuth.DefaultInstance.CurrentUser != null)
        {
            // User is logged in, go to main menu
            FastLoadingManager.LoadScene(mainMenuScene);
        }
        else
        {
            // User not logged in, go to login scene
            FastLoadingManager.LoadScene(loginScene);
        }
    }

    private void UpdateStatus(string message, float progress)
    {
        if (statusText != null)
            statusText.text = message;

        if (progressBar != null)
            progressBar.value = progress;

        Debug.Log($"Initialization: {message} ({progress * 100:F0}%)");
    }

    // Public method to check if initialization is complete
    public bool IsInitializationComplete()
    {
        return firebaseInitialized && adsInitialized && performanceOptimized;
    }
}
