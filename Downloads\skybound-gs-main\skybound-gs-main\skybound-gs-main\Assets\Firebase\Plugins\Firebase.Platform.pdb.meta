fileFormatVersion: 2
guid: af8fc0c835824578b855d4c0ed7b16ab
labels:
- gvh
- gvh_version-12.9.0
- gvhp_exportpath-Firebase/Plugins/Firebase.Platform.pdb
timeCreated: 1480838400
PluginImporter:
  serializedVersion: 1
  iconMap: {}
  executionOrder: {}
  isPreloaded: 0
  platformData:
    Android:
      enabled: 1
      settings:
        CPU: AnyCPU
    Any:
      enabled: 0
      settings: {}
    Editor:
      enabled: 1
      settings:
        CPU: AnyCPU
        DefaultValueInitialized: true
        OS: AnyOS
    Linux:
      enabled: 1
      settings:
        CPU: x86
    Linux64:
      enabled: 1
      settings:
        CPU: x86_64
    LinuxUniversal:
      enabled: 1
      settings:
        CPU: AnyCPU
    OSXIntel:
      enabled: 1
      settings:
        CPU: x86
    OSXIntel64:
      enabled: 1
      settings:
        CPU: x86_64
    OSXUniversal:
      enabled: 1
      settings:
        CPU: AnyCPU
    Web:
      enabled: 0
      settings: {}
    WebStreamed:
      enabled: 0
      settings: {}
    Win:
      enabled: 1
      settings:
        CPU: x86
    Win64:
      enabled: 1
      settings:
        CPU: x86_64
    WindowsStoreApps:
      enabled: 0
      settings:
        CPU: AnyCPU
    iOS:
      enabled: 1
      settings:
        CompileFlags:
        FrameworkDependencies:
    tvOS:
      enabled: 1
      settings:
        CompileFlags:
        FrameworkDependencies:
  userData:
  assetBundleName:
  assetBundleVariant:
