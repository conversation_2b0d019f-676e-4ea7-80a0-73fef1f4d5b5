%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Mk1_Flying_Anim
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves:
  - curve:
    - time: 0
      value: {fileID: 21300000, guid: 401bd8c92525f8e4294f3d2236127455, type: 3}
    - time: 0.13333334
      value: {fileID: 21300000, guid: 8cc5950944cb38b4d956a4717f7e02be, type: 3}
    - time: 0.26666668
      value: {fileID: 21300000, guid: bfe0fe1123913444490404f3ac019456, type: 3}
    - time: 0.4
      value: {fileID: 21300000, guid: 965722e6a144b414fa6acdca404ef17e, type: 3}
    - time: 0.53333336
      value: {fileID: 21300000, guid: 4abc6611b1aba884c9e506bea95c9cae, type: 3}
    - time: 0.65
      value: {fileID: 21300000, guid: 8cc5950944cb38b4d956a4717f7e02be, type: 3}
    - time: 0.78333336
      value: {fileID: 21300000, guid: bfe0fe1123913444490404f3ac019456, type: 3}
    - time: 0.8833333
      value: {fileID: 21300000, guid: 965722e6a144b414fa6acdca404ef17e, type: 3}
    - time: 1.0166667
      value: {fileID: 21300000, guid: 4abc6611b1aba884c9e506bea95c9cae, type: 3}
    - time: 1.1333333
      value: {fileID: 21300000, guid: 8cc5950944cb38b4d956a4717f7e02be, type: 3}
    - time: 1.2666667
      value: {fileID: 21300000, guid: bfe0fe1123913444490404f3ac019456, type: 3}
    - time: 1.4
      value: {fileID: 21300000, guid: 965722e6a144b414fa6acdca404ef17e, type: 3}
    - time: 1.5333333
      value: {fileID: 21300000, guid: 4abc6611b1aba884c9e506bea95c9cae, type: 3}
    - time: 1.6666666
      value: {fileID: 21300000, guid: 401bd8c92525f8e4294f3d2236127455, type: 3}
    attribute: m_Sprite
    path: 
    classID: 114
    script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 2015549526
      script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 1
    pptrCurveMapping:
    - {fileID: 21300000, guid: 401bd8c92525f8e4294f3d2236127455, type: 3}
    - {fileID: 21300000, guid: 8cc5950944cb38b4d956a4717f7e02be, type: 3}
    - {fileID: 21300000, guid: bfe0fe1123913444490404f3ac019456, type: 3}
    - {fileID: 21300000, guid: 965722e6a144b414fa6acdca404ef17e, type: 3}
    - {fileID: 21300000, guid: 4abc6611b1aba884c9e506bea95c9cae, type: 3}
    - {fileID: 21300000, guid: 8cc5950944cb38b4d956a4717f7e02be, type: 3}
    - {fileID: 21300000, guid: bfe0fe1123913444490404f3ac019456, type: 3}
    - {fileID: 21300000, guid: 965722e6a144b414fa6acdca404ef17e, type: 3}
    - {fileID: 21300000, guid: 4abc6611b1aba884c9e506bea95c9cae, type: 3}
    - {fileID: 21300000, guid: 8cc5950944cb38b4d956a4717f7e02be, type: 3}
    - {fileID: 21300000, guid: bfe0fe1123913444490404f3ac019456, type: 3}
    - {fileID: 21300000, guid: 965722e6a144b414fa6acdca404ef17e, type: 3}
    - {fileID: 21300000, guid: 4abc6611b1aba884c9e506bea95c9cae, type: 3}
    - {fileID: 21300000, guid: 401bd8c92525f8e4294f3d2236127455, type: 3}
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 1.6833333
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves: []
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
