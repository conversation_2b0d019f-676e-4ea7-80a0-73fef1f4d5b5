fileFormatVersion: 2
guid: 91cff42aa8b64de8ab09a800422f650f
labels:
- gvh
- gvh_version-10.3.0
- gvhp_exportpath-Firebase/Plugins/x86_64/FirebaseCppDatabase.dll
timeCreated: 1480838400
PluginImporter:
  serializedVersion: 1
  iconMap: {}
  executionOrder: {}
  isPreloaded: 0
  platformData:
    Android:
      enabled: 0
      settings:
        CPU: AnyCPU
    Any:
      enabled: 0
      settings: {}
    Editor:
      enabled: 1
      settings:
        CPU: AnyCPU
        DefaultValueInitialized: true
        OS: AnyOS
    Linux:
      enabled: 0
      settings:
        CPU: None
    Linux64:
      enabled: 0
      settings:
        CPU: None
    LinuxUniversal:
      enabled: 0
      settings:
        CPU: None
    OSXIntel:
      enabled: 0
      settings:
        CPU: None
    OSXIntel64:
      enabled: 0
      settings:
        CPU: None
    OSXUniversal:
      enabled: 0
      settings:
        CPU: None
    Web:
      enabled: 0
      settings: {}
    WebStreamed:
      enabled: 0
      settings: {}
    Win:
      enabled: 0
      settings:
        CPU: None
    Win64:
      enabled: 1
      settings:
        CPU: x86_64
    WindowsStoreApps:
      enabled: 0
      settings:
        CPU: AnyCPU
    iOS:
      enabled: 0
      settings:
        CompileFlags:
        FrameworkDependencies:
    tvOS:
      enabled: 0
      settings:
        CompileFlags:
        FrameworkDependencies:
  userData:
  assetBundleName:
  assetBundleVariant:
