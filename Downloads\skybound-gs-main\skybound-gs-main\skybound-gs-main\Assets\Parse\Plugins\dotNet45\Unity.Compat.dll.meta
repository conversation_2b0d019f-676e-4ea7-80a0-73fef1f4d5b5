fileFormatVersion: 2
guid: 3db0783b58c2429b9bc1f560ba323d05
labels:
- gvh
- gvh_dotnet-4.5
- gvh_version-6.13.0
- gvhp_exportpath-Parse/Plugins/dotNet45/Unity.Compat.dll
- gvhp_exportpath-Parse\Plugins\dotNet45\Unity.Compat.dll
PluginImporter:
  externalObjects: {}
  serializedVersion: 2
  iconMap: {}
  executionOrder: {}
  defineConstraints: []
  isPreloaded: 0
  isOverridable: 0
  isExplicitlyReferenced: 0
  validateReferences: 1
  platformData:
  - first:
      : 
    second:
      enabled: 1
      settings: {}
  - first:
      : Linux
    second:
      enabled: 1
      settings:
        CPU: x86
  - first:
      : LinuxUniversal
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      : OSXIntel
    second:
      enabled: 1
      settings:
        CPU: x86
  - first:
      : OSXIntel64
    second:
      enabled: 1
      settings:
        CPU: x86_64
  - first:
      : Web
    second:
      enabled: 1
      settings: {}
  - first:
      : WebStreamed
    second:
      enabled: 1
      settings: {}
  - first:
      Android: Android
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      Any: 
    second:
      enabled: 0
      settings: {}
  - first:
      Editor: Editor
    second:
      enabled: 1
      settings:
        DefaultValueInitialized: true
  - first:
      Nintendo Switch: Switch
    second:
      enabled: 1
      settings: {}
  - first:
      PS4: PS4
    second:
      enabled: 1
      settings: {}
  - first:
      Standalone: Linux64
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      Standalone: OSXUniversal
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      Standalone: Win
    second:
      enabled: 1
      settings:
        CPU: x86
  - first:
      Standalone: Win64
    second:
      enabled: 1
      settings:
        CPU: x86_64
  - first:
      WebGL: WebGL
    second:
      enabled: 1
      settings: {}
  - first:
      Windows Store Apps: WindowsStoreApps
    second:
      enabled: 1
      settings:
        CPU: AnyCPU
  - first:
      XboxOne: XboxOne
    second:
      enabled: 1
      settings: {}
  - first:
      iPhone: iOS
    second:
      enabled: 1
      settings:
        CompileFlags: 
        FrameworkDependencies: 
  - first:
      tvOS: tvOS
    second:
      enabled: 1
      settings: {}
  userData: 
  assetBundleName: 
  assetBundleVariant: 
