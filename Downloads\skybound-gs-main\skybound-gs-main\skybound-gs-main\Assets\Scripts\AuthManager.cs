using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Firebase;
using Firebase.Auth;
using TMPro;
using UnityEngine.SceneManagement;
using System;

public class AuthManager : MonoBehaviour
{
    public static bool hasCreatedAccount = false;
    public static string UserDisplayName { get; private set; }

    // Firebase variables
    [Header("Firebase")]
    private DependencyStatus dependencyStatus;
    private FirebaseAuth auth;
    public FirebaseUser User { get; private set; }

    // Login variables
    [Header("Login")]
    public TMP_InputField emailLoginField;
    public TMP_InputField passwordLoginField;
    public TMP_Text warningLoginText;
    public TMP_Text confirmLoginText;

    // Register variables
    [Header("Register")]
    public TMP_InputField usernameRegisterField;
    public TMP_InputField emailRegisterField;
    public TMP_InputField passwordRegisterField;
    public TMP_InputField passwordRegisterVerifyField;
    public TMP_Text warningRegisterText;

    // Guest variables
    [Header("GRegister")]
    public TMP_InputField gusernameRegisterField;

    // Loading indicator
    [Header("UI")]
    public GameObject loadingIndicator;

    private void Awake()
    {
        // Show loading indicator
        if (loadingIndicator != null)
            loadingIndicator.SetActive(true);

        // Check that all of the necessary dependencies for Firebase are present on the system
        FirebaseApp.CheckAndFixDependenciesAsync().ContinueWith(task =>
        {
            var dependencyStatus = task.Result;
            if (dependencyStatus == DependencyStatus.Available)
            {
                // Initialize Firebase
                InitializeFirebase();
            }
            else
            {
                Debug.LogError("Could not resolve all Firebase dependencies: " + dependencyStatus);
                HandleFirebaseError("Firebase initialization failed. Please check your internet connection.");
            }
        }, System.Threading.Tasks.TaskScheduler.FromCurrentSynchronizationContext());
    }

    private void InitializeFirebase()
    {
        try
        {
            auth = FirebaseAuth.DefaultInstance;

            // Hide loading indicator
            if (loadingIndicator != null)
                loadingIndicator.SetActive(false);

            Debug.Log("Firebase Auth initialized successfully");
        }
        catch (Exception e)
        {
            Debug.LogError("Firebase initialization error: " + e.Message);
            HandleFirebaseError("Authentication service unavailable.");
        }
    }

    private void HandleFirebaseError(string message)
    {
        // Hide loading indicator
        if (loadingIndicator != null)
            loadingIndicator.SetActive(false);

        // Show error message to user
        if (warningLoginText != null)
            warningLoginText.text = message;
        if (warningRegisterText != null)
            warningRegisterText.text = message;
    }

    public void LoginButton()
    {
        // Verify input fields
        if (string.IsNullOrEmpty(emailLoginField?.text) || string.IsNullOrEmpty(passwordLoginField?.text))
        {
            if (warningLoginText != null)
                warningLoginText.text = "Email and Password fields cannot be empty";
            return;
        }

        // Clear previous warnings
        if (warningLoginText != null)
            warningLoginText.text = "";

        StartCoroutine(Login(emailLoginField.text, passwordLoginField.text));
    }
    public void GuestButton()
    {
        if (string.IsNullOrEmpty(gusernameRegisterField?.text))
        {
            if (warningRegisterText != null)
                warningRegisterText.text = "Username field cannot be empty";
            return;
        }

        // Clear previous warnings
        if (warningRegisterText != null)
            warningRegisterText.text = "";

        UserDisplayName = gusernameRegisterField.text;
        StartCoroutine(SignInAnonymously());
    }

    private IEnumerator SignInAnonymously()
    {
        if (auth == null)
        {
            if (warningRegisterText != null)
                warningRegisterText.text = "Authentication service not available";
            yield break;
        }

        var signInTask = auth.SignInAnonymouslyAsync();
        yield return new WaitUntil(() => signInTask.IsCompleted);

        if (signInTask.Exception != null)
        {
            Debug.LogError("SignInAnonymouslyAsync encountered an error: " + signInTask.Exception);

            if (warningRegisterText != null)
                warningRegisterText.text = "Failed to sign in as guest. Please try again.";
        }
        else
        {
            User = signInTask.Result;
            UserDisplayName = gusernameRegisterField.text;

            Debug.Log("Guest sign-in successful");

            // Load main menu scene
            SceneManager.LoadScene(2);
        }
    }

    public void RegisterButton()
    {
        // Verify input fields
        if (string.IsNullOrEmpty(usernameRegisterField?.text) ||
            string.IsNullOrEmpty(emailRegisterField?.text) ||
            string.IsNullOrEmpty(passwordRegisterField?.text) ||
            string.IsNullOrEmpty(passwordRegisterVerifyField?.text))
        {
            if (warningRegisterText != null)
                warningRegisterText.text = "All fields are required";
            return;
        }

        // Verify password match
        if (passwordRegisterField.text != passwordRegisterVerifyField.text)
        {
            if (warningRegisterText != null)
                warningRegisterText.text = "Passwords do not match";
            return;
        }

        // Clear previous warnings
        if (warningRegisterText != null)
            warningRegisterText.text = "";

        StartCoroutine(Register(emailRegisterField.text, passwordRegisterField.text, usernameRegisterField.text));
    }

    public IEnumerator Login(string _email, string _password)
    {
        if (auth == null)
        {
            if (warningLoginText != null)
                warningLoginText.text = "Authentication service not available";
            yield break;
        }

        var loginTask = auth.SignInWithEmailAndPasswordAsync(_email, _password);
        yield return new WaitUntil(() => loginTask.IsCompleted);

        if (loginTask.Exception != null)
        {
            Debug.LogError("Login failed: " + loginTask.Exception);

            string message = "Login Failed!";

            if (loginTask.Exception.GetBaseException() is FirebaseException firebaseEx)
            {
                AuthError errorCode = (AuthError)firebaseEx.ErrorCode;
                switch (errorCode)
                {
                    case AuthError.MissingEmail:
                        message = "Missing Email";
                        break;
                    case AuthError.MissingPassword:
                        message = "Missing Password";
                        break;
                    case AuthError.WrongPassword:
                        message = "Wrong Password";
                        break;
                    case AuthError.InvalidEmail:
                        message = "Invalid Email";
                        break;
                    case AuthError.UserNotFound:
                        message = "Account does not exist";
                        break;
                    case AuthError.NetworkError:
                        message = "Network error. Please check your connection.";
                        break;
                    default:
                        message = "Login failed: " + errorCode.ToString();
                        break;
                }
            }

            if (warningLoginText != null)
                warningLoginText.text = message;
        }
        else
        {
            User = loginTask.Result;
            UserDisplayName = User.DisplayName ?? "User";

            if (warningLoginText != null)
                warningLoginText.text = "";
            if (confirmLoginText != null)
                confirmLoginText.text = "Logged In";

            // Load main menu scene
            SceneManager.LoadScene(2);
        }
    }

    public IEnumerator Register(string _email, string _password, string _username)
    {
        if (auth == null)
        {
            if (warningRegisterText != null)
                warningRegisterText.text = "Authentication service not available";
            yield break;
        }

        var registerTask = auth.CreateUserWithEmailAndPasswordAsync(_email, _password);
        yield return new WaitUntil(() => registerTask.IsCompleted);

        if (registerTask.Exception != null)
        {
            Debug.LogError("Registration failed: " + registerTask.Exception);

            string message = "Registration Failed!";

            if (registerTask.Exception.GetBaseException() is FirebaseException firebaseEx)
            {
                AuthError errorCode = (AuthError)firebaseEx.ErrorCode;
                switch (errorCode)
                {
                    case AuthError.MissingEmail:
                        message = "Missing Email";
                        break;
                    case AuthError.MissingPassword:
                        message = "Missing Password";
                        break;
                    case AuthError.WeakPassword:
                        message = "Password should be at least 6 characters";
                        break;
                    case AuthError.InvalidEmail:
                        message = "Invalid Email";
                        break;
                    case AuthError.EmailAlreadyInUse:
                        message = "Email already in use";
                        break;
                    case AuthError.NetworkError:
                        message = "Network error. Please check your connection.";
                        break;
                    default:
                        message = "Registration failed: " + errorCode.ToString();
                        break;
                }
            }

            if (warningRegisterText != null)
                warningRegisterText.text = message;
        }
        else
        {
            // Successful registration
            User = registerTask.Result;
            UserDisplayName = _username;
            hasCreatedAccount = true;

            // Update user profile with display name
            UpdateUserProfile(_username);

            if (warningRegisterText != null)
                warningRegisterText.text = "Welcome";

            // Load main menu scene
            SceneManager.LoadScene(2);
        }
    }

    public void UpdateUserProfile(string _username)
    {
        if (auth?.CurrentUser == null)
        {
            Debug.LogWarning("Cannot update user profile: No current user");
            return;
        }

        UserProfile profile = new UserProfile
        {
            DisplayName = _username
        };

        var updateTask = auth.CurrentUser.UpdateUserProfileAsync(profile);
        updateTask.ContinueWith(task =>
        {
            if (task.IsCompleted && !task.IsFaulted)
            {
                Debug.Log("User profile updated successfully");
                UserDisplayName = _username;

                if (warningRegisterText != null)
                    warningRegisterText.text = "";
                if (confirmLoginText != null)
                    confirmLoginText.text = "Profile updated successfully!";
            }
            else
            {
                Debug.LogError("Error updating user profile: " + task.Exception);
                if (warningRegisterText != null)
                    warningRegisterText.text = "Error updating user profile";
            }
        }, System.Threading.Tasks.TaskScheduler.FromCurrentSynchronizationContext());
    }
}