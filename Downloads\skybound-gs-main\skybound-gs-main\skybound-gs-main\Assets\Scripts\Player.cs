using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Player : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
{
    [Head<PERSON>("Player Settings")]
    public float velocity = 1;

    private Rigidbody2D rb;
    private Vector2 startPos;
    private GameManager gameManager;
    private bool isGameActive = true;

    private void Awake()
    {
        startPos = transform.position;
    }

    // Start is called before the first frame update
    void Start()
    {
        rb = GetComponent<Rigidbody2D>();

        // Cache GameManager reference for better performance
        gameManager = FindObjectOfType<GameManager>();

        if (gameManager == null)
        {
            Debug.LogWarning("GameManager not found in scene!");
        }
    }

    // Update is called once per frame
    void Update()
    {
        if (!isGameActive) return;

        // Support both mouse and touch input
        if (Input.GetMouseButtonDown(0) || (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Began))
        {
            Jump();
        }
    }

    private void Jump()
    {
        if (rb != null)
        {
            rb.velocity = Vector2.up * velocity;
        }
    }

    public void ResetPos()
    {
        transform.position = startPos;
        if (rb != null)
        {
            rb.velocity = Vector2.zero;
        }
        isGameActive = true;
    }

    public void SetGameActive(bool active)
    {
        isGameActive = active;
    }

    private void OnCollisionEnter2D(Collision2D collision)
    {
        if (!isGameActive) return;

        isGameActive = false;

        if (gameManager != null)
        {
            gameManager.GameOver();
        }
        else
        {
            Debug.LogError("GameManager reference is null!");
        }
    }
}
