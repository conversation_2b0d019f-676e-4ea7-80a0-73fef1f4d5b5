fileFormatVersion: 2
guid: 153ea3bf2adc4ebd843e94004af375f2
labels:
- gvh
- gvh_version-10.3.0
- gvhp_exportpath-Firebase/Plugins/iOS/Firebase.Firestore.dll
timeCreated: 1480838400
PluginImporter:
  serializedVersion: 1
  iconMap: {}
  executionOrder: {}
  isPreloaded: 0
  platformData:
    Android:
      enabled: 0
      settings:
        CPU: AnyCPU
    Any:
      enabled: 0
      settings: {}
    Editor:
      enabled: 0
      settings:
        CPU: AnyCPU
        DefaultValueInitialized: true
        OS: AnyOS
    Linux:
      enabled: 0
      settings:
        CPU: None
    Linux64:
      enabled: 0
      settings:
        CPU: None
    LinuxUniversal:
      enabled: 0
      settings:
        CPU: None
    OSXIntel:
      enabled: 0
      settings:
        CPU: None
    OSXIntel64:
      enabled: 0
      settings:
        CPU: None
    OSXUniversal:
      enabled: 0
      settings:
        CPU: None
    Web:
      enabled: 0
      settings: {}
    WebStreamed:
      enabled: 0
      settings: {}
    Win:
      enabled: 0
      settings:
        CPU: None
    Win64:
      enabled: 0
      settings:
        CPU: None
    WindowsStoreApps:
      enabled: 0
      settings:
        CPU: AnyCPU
    iOS:
      enabled: 1
      settings:
        CompileFlags:
        FrameworkDependencies:
    tvOS:
      enabled: 1
      settings:
        CompileFlags:
        FrameworkDependencies:
  userData:
  assetBundleName:
  assetBundleVariant:
