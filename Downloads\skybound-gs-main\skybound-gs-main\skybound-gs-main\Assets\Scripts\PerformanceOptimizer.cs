using UnityEngine;
using System.Collections;

public class PerformanceOptimizer : MonoBehaviour
{
    [Header("Performance Settings")]
    [SerializeField] private int targetFrameRate = 60;
    [SerializeField] private bool enableVSync = false;
    [SerializeField] private bool optimizeForMobile = true;

    [Header("Quality Settings")]
    [SerializeField] private bool autoAdjustQuality = true;
    [SerializeField] private float performanceCheckInterval = 2f;

    private float averageFrameRate;
    private int frameCount;
    private float frameTimer;

    private void Awake()
    {
        // Apply initial optimizations
        ApplyPerformanceSettings();

        if (autoAdjustQuality)
        {
            StartCoroutine(MonitorPerformance());
        }
    }

    private void ApplyPerformanceSettings()
    {
        // Set target frame rate
        Application.targetFrameRate = targetFrameRate;

        // Configure VSync
        QualitySettings.vSyncCount = enableVSync ? 1 : 0;

        // Mobile optimizations
        if (optimizeForMobile && Application.isMobilePlatform)
        {
            // Reduce quality for better performance on mobile
            QualitySettings.SetQualityLevel(2, true); // Medium quality

            // Optimize physics for Unity 2022.1
            Physics2D.simulationMode = SimulationMode2D.FixedUpdate;
            Time.fixedDeltaTime = 1f / 50f; // 50 FPS physics

            // Reduce shadow quality
            QualitySettings.shadows = ShadowQuality.Disable;

            // Optimize rendering for Unity 2022.1
            QualitySettings.antiAliasing = 0;
            QualitySettings.anisotropicFiltering = AnisotropicFiltering.Disable;

            // Unity 2022.1 specific optimizations
            QualitySettings.softParticles = false;
            QualitySettings.realtimeReflectionProbes = false;
            QualitySettings.billboardsFaceCameraPosition = false;

            // Optimize texture streaming
            QualitySettings.streamingMipmapsActive = true;
            QualitySettings.streamingMipmapsMemoryBudget = 512; // 512MB for mobile
        }
        else
        {
            // Desktop optimizations for Unity 2022.1
            QualitySettings.streamingMipmapsActive = true;
            QualitySettings.streamingMipmapsMemoryBudget = 1024; // 1GB for desktop
        }

        // Memory optimizations
        System.GC.Collect();
        Resources.UnloadUnusedAssets();
    }

    private IEnumerator MonitorPerformance()
    {
        while (true)
        {
            yield return new WaitForSeconds(performanceCheckInterval);

            float currentFPS = 1f / Time.unscaledDeltaTime;

            // If performance is poor, reduce quality
            if (currentFPS < targetFrameRate * 0.8f)
            {
                OptimizeForLowPerformance();
            }
            // If performance is good, we can increase quality slightly
            else if (currentFPS > targetFrameRate * 1.1f)
            {
                OptimizeForHighPerformance();
            }
        }
    }

    private void OptimizeForLowPerformance()
    {
        Debug.Log("Performance low, applying optimizations...");

        // Reduce quality settings
        if (QualitySettings.GetQualityLevel() > 0)
        {
            QualitySettings.DecreaseLevel(true);
        }

        // Reduce physics update rate
        Time.fixedDeltaTime = 1f / 30f;

        // Force garbage collection
        System.GC.Collect();
        Resources.UnloadUnusedAssets();
    }

    private void OptimizeForHighPerformance()
    {
        // Only increase quality if we're not at max level
        if (QualitySettings.GetQualityLevel() < QualitySettings.names.Length - 1)
        {
            QualitySettings.IncreaseLevel(true);
        }
    }

    private void Update()
    {
        // Calculate average FPS
        frameCount++;
        frameTimer += Time.unscaledDeltaTime;

        if (frameTimer >= 1f)
        {
            averageFrameRate = frameCount / frameTimer;
            frameCount = 0;
            frameTimer = 0f;
        }
    }

    // Public method to get current FPS
    public float GetAverageFPS()
    {
        return averageFrameRate;
    }

    // Public method to force optimization
    public void ForceOptimization()
    {
        ApplyPerformanceSettings();
    }

    // Clean up memory
    public void CleanupMemory()
    {
        System.GC.Collect();
        Resources.UnloadUnusedAssets();
    }
}
