%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!74 &7400000
AnimationClip:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: MK2_Flying_Anim
  serializedVersion: 6
  m_Legacy: 0
  m_Compressed: 0
  m_UseHighQualityCurve: 1
  m_RotationCurves: []
  m_CompressedRotationCurves: []
  m_EulerCurves: []
  m_PositionCurves: []
  m_ScaleCurves: []
  m_FloatCurves: []
  m_PPtrCurves:
  - curve:
    - time: 0
      value: {fileID: 21300000, guid: e5a9a7de823b1e546ae5a8ccdff048dd, type: 3}
    - time: 0.13333334
      value: {fileID: 21300000, guid: a7c77334617d3e8499cf562b5893f405, type: 3}
    - time: 0.25
      value: {fileID: 21300000, guid: da7bb29f7939e2345a57cc67f5fcc4fa, type: 3}
    - time: 0.38333333
      value: {fileID: 21300000, guid: 98bcd8640a4f95e43b114f5a7c07015c, type: 3}
    - time: 0.51666665
      value: {fileID: 21300000, guid: a86e0e47827df124a964ea53fdc80ba0, type: 3}
    - time: 0.6333333
      value: {fileID: 21300000, guid: a7c77334617d3e8499cf562b5893f405, type: 3}
    - time: 0.76666665
      value: {fileID: 21300000, guid: da7bb29f7939e2345a57cc67f5fcc4fa, type: 3}
    - time: 0.9
      value: {fileID: 21300000, guid: 98bcd8640a4f95e43b114f5a7c07015c, type: 3}
    - time: 1.0333333
      value: {fileID: 21300000, guid: a86e0e47827df124a964ea53fdc80ba0, type: 3}
    - time: 1.15
      value: {fileID: 21300000, guid: a7c77334617d3e8499cf562b5893f405, type: 3}
    - time: 1.2833333
      value: {fileID: 21300000, guid: da7bb29f7939e2345a57cc67f5fcc4fa, type: 3}
    - time: 1.4166666
      value: {fileID: 21300000, guid: 98bcd8640a4f95e43b114f5a7c07015c, type: 3}
    - time: 1.5333333
      value: {fileID: 21300000, guid: a86e0e47827df124a964ea53fdc80ba0, type: 3}
    - time: 1.6666666
      value: {fileID: 21300000, guid: e5a9a7de823b1e546ae5a8ccdff048dd, type: 3}
    attribute: m_Sprite
    path: 
    classID: 114
    script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_SampleRate: 60
  m_WrapMode: 0
  m_Bounds:
    m_Center: {x: 0, y: 0, z: 0}
    m_Extent: {x: 0, y: 0, z: 0}
  m_ClipBindingConstant:
    genericBindings:
    - serializedVersion: 2
      path: 0
      attribute: 2015549526
      script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
      typeID: 114
      customType: 0
      isPPtrCurve: 1
    pptrCurveMapping:
    - {fileID: 21300000, guid: e5a9a7de823b1e546ae5a8ccdff048dd, type: 3}
    - {fileID: 21300000, guid: a7c77334617d3e8499cf562b5893f405, type: 3}
    - {fileID: 21300000, guid: da7bb29f7939e2345a57cc67f5fcc4fa, type: 3}
    - {fileID: 21300000, guid: 98bcd8640a4f95e43b114f5a7c07015c, type: 3}
    - {fileID: 21300000, guid: a86e0e47827df124a964ea53fdc80ba0, type: 3}
    - {fileID: 21300000, guid: a7c77334617d3e8499cf562b5893f405, type: 3}
    - {fileID: 21300000, guid: da7bb29f7939e2345a57cc67f5fcc4fa, type: 3}
    - {fileID: 21300000, guid: 98bcd8640a4f95e43b114f5a7c07015c, type: 3}
    - {fileID: 21300000, guid: a86e0e47827df124a964ea53fdc80ba0, type: 3}
    - {fileID: 21300000, guid: a7c77334617d3e8499cf562b5893f405, type: 3}
    - {fileID: 21300000, guid: da7bb29f7939e2345a57cc67f5fcc4fa, type: 3}
    - {fileID: 21300000, guid: 98bcd8640a4f95e43b114f5a7c07015c, type: 3}
    - {fileID: 21300000, guid: a86e0e47827df124a964ea53fdc80ba0, type: 3}
    - {fileID: 21300000, guid: e5a9a7de823b1e546ae5a8ccdff048dd, type: 3}
  m_AnimationClipSettings:
    serializedVersion: 2
    m_AdditiveReferencePoseClip: {fileID: 0}
    m_AdditiveReferencePoseTime: 0
    m_StartTime: 0
    m_StopTime: 1.6833333
    m_OrientationOffsetY: 0
    m_Level: 0
    m_CycleOffset: 0
    m_HasAdditiveReferencePose: 0
    m_LoopTime: 1
    m_LoopBlend: 0
    m_LoopBlendOrientation: 0
    m_LoopBlendPositionY: 0
    m_LoopBlendPositionXZ: 0
    m_KeepOriginalOrientation: 0
    m_KeepOriginalPositionY: 1
    m_KeepOriginalPositionXZ: 0
    m_HeightFromFeet: 0
    m_Mirror: 0
  m_EditorCurves: []
  m_EulerEditorCurves: []
  m_HasGenericRootTransform: 0
  m_HasMotionFloatCurves: 0
  m_Events: []
