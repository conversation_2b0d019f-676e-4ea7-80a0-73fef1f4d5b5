﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Firebase;
using Firebase.Auth;
using TMPro;
using UnityEngine.SceneManagement;
using System;

public class AuthManager : MonoBehaviour
{
    public static bool hasCreatedAccount = false;
    public static string UserDisplayName { get; private set; }

    // Firebase variables
    [Header("Firebase")]
    private DependencyStatus dependencyStatus;
    private FirebaseAuth auth;
    public FirebaseUser User { get; private set; }

    // Login variables
    [Header("Login")]
    public TMP_InputField emailLoginField;
    public TMP_InputField passwordLoginField;
    public TMP_Text warningLoginText;
    public TMP_Text confirmLoginText;

    // Register variables
    [Header("Register")]
    public TMP_InputField usernameRegisterField;
    public TMP_InputField emailRegisterField;
    public TMP_InputField passwordRegisterField;
    public TMP_InputField passwordRegisterVerifyField;
    public TMP_Text warningRegisterText;

    // Guest variables
    [Header("GRegister")]
    public TMP_InputField gusernameRegisterField;

    // Loading indicator
    [Header("UI")]
    public GameObject loadingIndicator;

    private void Awake()
    {
        // Show loading indicator
        if (loadingIndicator != null)
            loadingIndicator.SetActive(true);

        // Check that all of the necessary dependencies for Firebase are present on the system
        FirebaseApp.CheckAndFixDependenciesAsync().ContinueWith(task =>
        {
            dependencyStatus = task.Result;
            if (dependencyStatus == DependencyStatus.Available)
            {
                InitializeFirebase();
            }
            else
            {
                Debug.LogError("Could not resolve all Firebase dependencies: " + dependencyStatus);
                HandleFirebaseError("Firebase initialization failed. Please check your internet connection.");
            }
        });
    }

    private void InitializeFirebase()
    {
        try
        {
            auth = FirebaseAuth.DefaultInstance;

            // Hide loading indicator
            if (loadingIndicator != null)
                loadingIndicator.SetActive(false);

            Debug.Log("Firebase Auth initialized successfully");
        }
        catch (Exception e)
        {
            Debug.LogError("Firebase initialization error: " + e.Message);
            HandleFirebaseError("Authentication service unavailable.");
        }
    }

    private void HandleFirebaseError(string message)
    {
        // Hide loading indicator
        if (loadingIndicator != null)
            loadingIndicator.SetActive(false);

        // Show error message to user
        if (warningLoginText != null)
            warningLoginText.text = message;
        if (warningRegisterText != null)
            warningRegisterText.text = message;
    }

    public void LoginButton()
    {
        // Verify input fields
        if (string.IsNullOrEmpty(emailLoginField.text) || string.IsNullOrEmpty(passwordLoginField.text))
        {
            warningLoginText.text = "Email and Password fields cannot be empty";
            return;
        }

        StartCoroutine(Login(emailLoginField.text, passwordLoginField.text));
    }
    public void GuestButton()
{
    if (string.IsNullOrEmpty(gusernameRegisterField.text))
    {
        warningRegisterText.text = "Username field cannot be empty";
        return;
    }

    UserDisplayName = gusernameRegisterField.text;
    StartCoroutine(SignInAnonymously());
}

private IEnumerator SignInAnonymously()
{
    var signInTask = auth.SignInAnonymouslyAsync();
    yield return new WaitUntil(() => signInTask.IsCompleted);

    if (signInTask.Exception != null)
    {
        Debug.LogError("SignInAnonymouslyAsync encountered an error: " + signInTask.Exception);
    }
    else
    {
        User = signInTask.Result;
        UserDisplayName = gusernameRegisterField.text;
        SceneManager.LoadScene(2);
    }
}

    public void RegisterButton()
    {
        // Verify input fields
        if (string.IsNullOrEmpty(usernameRegisterField.text) ||
            string.IsNullOrEmpty(emailRegisterField.text) ||
            string.IsNullOrEmpty(passwordRegisterField.text) ||
            string.IsNullOrEmpty(passwordRegisterVerifyField.text))
        {
            warningRegisterText.text = "All fields are required";
            return;
        }
        // Verify password match
        if (passwordRegisterField.text != passwordRegisterVerifyField.text)
        {
            warningRegisterText.text = "Passwords do not match";
            return;
        }
            StartCoroutine(Register(emailRegisterField.text, passwordRegisterField.text, usernameRegisterField.text));
    }

    public IEnumerator Login(string _email, string _password)
{
    var loginTask = auth.SignInWithEmailAndPasswordAsync(_email, _password);
    yield return new WaitUntil(() => loginTask.IsCompleted);

    if (loginTask.Exception != null)
    {
        FirebaseException firebaseEx = loginTask.Exception.GetBaseException() as FirebaseException;
        AuthError errorCode = (AuthError)firebaseEx.ErrorCode;

        string message = "Login Failed!";
        switch (errorCode)
        {
            case AuthError.MissingEmail:
                message = "Missing Email";
                break;
            case AuthError.MissingPassword:
                message = "Missing Password";
                break;
            case AuthError.WrongPassword:
                message = "Wrong Password";
                break;
            case AuthError.InvalidEmail:
                message = "Invalid Email";
                break;
            case AuthError.UserNotFound:
                message = "Account does not exist";
                break;
        }
        warningLoginText.text = message;
    }
    else
    {
        User = loginTask.Result;
        UserDisplayName = User.DisplayName;
        warningLoginText.text = "";
        confirmLoginText.text = "Logged In";
        SceneManager.LoadScene(2);
    }
}

public IEnumerator Register(string _email, string _password, string _username)
{
    var registerTask = auth.CreateUserWithEmailAndPasswordAsync(_email, _password);
    yield return new WaitUntil(() => registerTask.IsCompleted);
    hasCreatedAccount = true;


    if (registerTask.Exception != null)
    {
        FirebaseException firebaseEx = registerTask.Exception.GetBaseException() as FirebaseException;
        AuthError errorCode = (AuthError)firebaseEx.ErrorCode;

        string message = "Registration Failed!";
        switch (errorCode)
        {
            case AuthError.MissingEmail:
                message = "Missing Email";
                break;
            case AuthError.MissingPassword:
                message = "Missing Password";
                break;
            case AuthError.WeakPassword:
                message = "Weak Password";
                break;
            case AuthError.InvalidEmail:
                message = "Invalid Email";
                break;
            case AuthError.EmailAlreadyInUse:
                message = "Email already in use";
                break;
        }
        warningRegisterText.text = message;
    }
    else
    {
        // Successful registration
        User = registerTask.Result;
        UserDisplayName = _username;
        warningRegisterText.text = "Welcome";
        SceneManager.LoadScene(2);
    }
}

public void UpdateUserProfile(string _username)
{
    UserProfile profile = new UserProfile
    {
        DisplayName = _username
    };

    var updateTask = auth.CurrentUser.UpdateUserProfileAsync(profile);
    updateTask.ContinueWith(task =>
    {
        if (task.IsCompleted)
        {
            warningRegisterText.text = "";
            confirmLoginText.text = "Registration successful! Please log in.";
        }
        else
        {
            warningRegisterText.text = "Error updating user profile";
        }
    });
}
}